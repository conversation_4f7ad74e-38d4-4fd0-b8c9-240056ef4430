# Assistente Virtuale per Documentazione di Prodotto

Questo progetto implementa un chatbot basato su AI in grado di rispondere a domande degli utenti basandosi su una serie di documenti PDF specifici per un prodotto. Utilizza Jina AI per la generazione di embeddings, Google Gemini per la creazione di risposte conversazionali e ChromaDB per l'archiviazione e la ricerca vettoriale.

L'applicazione offre due interfacce utente: una chat a pagina intera e un widget di chat "embedded" progettato per essere facilmente integrato in siti web di terze parti.

## Indice

- [Caratteristiche Principali](#caratteristiche-principali)
- [Come Funziona](#come-funziona)
- [Struttura del Progetto](#struttura-del-progetto)
- [Installazione e Configurazione](#installazione-e-configurazione)
- [Utilizzo](#utilizzo)
- [Integrazione del Widget Embedded](#integrazione-del-widget-embedded)
- [Configurazione Avanzata](#configurazione-avanzata)
- [Endpoint API](#endpoint-api)
- [Correzione Bug: Link ai Documenti Rimossi dalle Risposte](#correzione-bug-link-ai-documenti-rimossi-dalle-risposte)

## Caratteristiche Principali

- **Elaborazione Dinamica dei PDF**: Indicizza automaticamente i documenti PDF associati a un codice prodotto.
- **Ricerca Semantica**: Utilizza embeddings vettoriali per trovare le informazioni più pertinenti all'interno dei documenti.
- **Risposte Contestuali**: Sfrutta la cronologia della conversazione per interpretare meglio l'intento dell'utente e fornire risposte più accurate.
- **Verbosità Configurabile**: Permette di impostare il livello di dettaglio delle risposte del chatbot (da 1 a 5).
- **Riferimenti ai Documenti**: Le risposte includono link diretti alle pagine dei PDF da cui sono state estratte le informazioni.
- **Doppia Interfaccia**: Offre sia una versione a pagina intera che un widget "embedded" per la massima flessibilità.
- **Caching Intelligente**: Rielabora solo i file PDF che sono stati modificati, per ottimizzare i tempi di avvio.

## Come Funziona

L'architettura del sistema si basa su un backend Python che utilizza il micro-framework Flask e un frontend HTML, CSS e JavaScript.

1.  **Backend (Python/Flask)**:
    -   `app.py`: Il file principale che gestisce le rotte web, le richieste API e l'inizializzazione del chatbot.
    -   `pdf_chatbot_prodotti.py`: Contiene la logica di base del chatbot (`ProductPDFChatbot`). Questa classe si occupa di:
        -   **Elaborazione dei PDF**: Estrarre il testo dai file PDF.
        -   **Creazione di Embeddings**: Convertire i chunk di testo in vettori numerici utilizzando l'API di Jina.
        -   **Indicizzazione**: Salvare i vettori e i metadati associati in un database ChromaDB.
        -   **Ricerca**: Eseguire ricerche di similarità per trovare i chunk di testo più rilevanti per una data domanda.
        -   **Generazione Risposta**: Inviare la domanda, la cronologia della chat e il contesto recuperato al modello Gemini per generare una risposta in linguaggio naturale.

2.  **Frontend (HTML/CSS/JS)**:
    -   `templates/index.html`: Template per la chat a pagina intera.
    -   `templates/embedded.html`: Template per il widget di chat.
    -   `static/`: Contiene i file CSS e JavaScript che gestiscono lo stile e l'interattività delle interfacce.

## Struttura del Progetto

```
/
├── app.py                  # Applicazione principale Flask
├── pdf_chatbot_prodotti.py # Logica del chatbot
├── requirements.txt        # Dipendenze Python
├── .env                    # File per le variabili d'ambiente (da creare)
├── .gitignore              # File ignorati da Git
├── README.md               # Questo file
├── pdf/                    # Cartella contenente i PDF dei prodotti
│   └── CODICE_PRODOTTO_A/
│       └── manuale.pdf
├── chromadb_data/          # Database vettoriale di ChromaDB
├── guardrails_py/          # Moduli di sicurezza per il chatbot
│   ├── __init__.py
│   ├── guardrail_manager.py
│   ├── input/
│   │   ├── __init__.py
│   │   ├── input_length_control_guardrail.py
│   │   ├── language_detection_guardrail.py
│   │   └── pii_filter_guardrail.py
│   └── output/
│       ├── __init__.py
│       ├── harmful_content_filter_guardrail.py
│       └── bias_fairness_mitigation_guardrail.py
├── static/                 # File statici (CSS, JS)
│   ├── css/
│   │   ├── style.css
│   │   └── embedded.css
│   └── js/
│       ├── script.js
│       └── embedded.js
└── templates/              # Template HTML di Flask
    ├── index.html
    └── embedded.html
```

## Installazione e Configurazione

Segui questi passaggi per eseguire il progetto in locale.

**Prerequisiti**:
- Python 3.8+
- `pip` (gestore di pacchetti Python)

**1. Clona il Repository**
```bash
git clone <URL_DEL_TUO_REPOSITORY>
cd <NOME_DELLA_CARTELLA>
```

**2. Crea un Ambiente Virtuale**
È consigliabile utilizzare un ambiente virtuale per isolare le dipendenze del progetto.
```bash
python -m venv env
source env/bin/activate  # Su Windows: env\Scripts\activate
```

**3. Installa le Dipendenze**
```bash
pip install -r requirements.txt
```

**4. Configura le Variabili d'Ambiente**
Crea un file chiamato `.env` nella root del progetto e aggiungi le tue API key e le configurazioni desiderate.

```dotenv
# .env
JINA_API_KEY="tua_api_key_jina"
GEMINI_API_KEY="tua_api_key_gemini"
VERBOSITY_LEVEL=3
```

- `JINA_API_KEY`: La tua chiave API per Jina AI.
- `GEMINI_API_KEY`: La tua chiave API per Google Gemini.
- `VERBOSITY_LEVEL`: Il livello di dettaglio delle risposte (da 1 a 5).

**5. Aggiungi i Documenti PDF**
Crea una sottocartella all'interno della cartella `pdf/` per ogni codice prodotto. Il nome della sottocartella deve corrispondere esattamente al codice prodotto che userai nell'interfaccia.

Esempio:
- `pdf/CODICE_A/manuale1.pdf`
- `pdf/CODICE_A/manuale2.pdf`
- `pdf/CODICE_B/specifiche.pdf`

## Utilizzo

Una volta completata la configurazione, avvia l'applicazione Flask:

```bash
python app.py
```

L'applicazione sarà accessibile ai seguenti indirizzi:
- **Chat a Pagina Intera**: `http://127.0.0.1:5000/`
- **Widget Embedded**: `http://127.0.0.1:5000/embedded`

Al primo avvio per un nuovo codice prodotto, il sistema richiederà del tempo per elaborare e indicizzare i documenti PDF. Le esecuzioni successive saranno molto più veloci.

## Integrazione del Widget Embedded

Per integrare il chatbot in una qualsiasi pagina web esterna, aggiungi il seguente codice HTML. Puoi personalizzare le dimensioni (`width` e `height`) secondo le tue necessità.

```html
<iframe 
    src="http://127.0.0.1:5000/embedded" 
    width="400" 
    height="600" 
    style="border:none; position:fixed; bottom:20px; right:20px; z-index: 9999;"
    title="Assistente Virtuale">
</iframe>
```

Questo `<iframe>` mostrerà il pulsante di avvio del chatbot nell'angolo in basso a destra della pagina ospitante.

## Guardrails

Il sistema integra una serie di "guardrails" per garantire la sicurezza, l'affidabilità e l'eticità delle interazioni del chatbot. Questi moduli, implementati in Python e situati nella cartella `guardrails_py`, analizzano sia l'input dell'utente che l'output del modello AI per prevenire abusi e contenuti inappropriati.

### Fix e Ottimizzazioni del Sistema Guardrails

Il sistema guardrails è stato recentemente ottimizzato per migliorare le performance e ridurre le dipendenze esterne.

**Problemi Risolti:**
-   **Dipendenze Mancanti**: Il sistema falliva a causa di dipendenze esterne pesanti (`langdetect`, `presidio_analyzer`, `presidio_anonymizer`, `spacy`). Queste sono state sostituite con implementazioni leggere basate su regex.
-   **Struttura del Pacchetto Mancante**: I file `__init__.py` mancanti impedivano l'importazione corretta dei pacchetti Python. Sono stati creati i file necessari per una struttura di pacchetto adeguata (`guardrails_py/__init__.py`, `guardrails_py/input/__init__.py`, `guardrails_py/output/__init__.py`).

**Ottimizzazioni Implementate:**
-   **Sostituzione Rilevamento Linguaggio**: Invece della libreria `langdetect`, è stata implementata una soluzione leggera basata su regex per il rilevamento della lingua (italiano e inglese), che supporta il punteggio di confidenza e non ha dipendenze esterne, risultando in una elaborazione più veloce.
-   **Sostituzione Rilevamento PII**: Invece di Presidio con il modello spaCy, è stata implementata una rilevazione PII completa basata su regex per indirizzi email, numeri di telefono (italiani e internazionali), numeri di carte di credito, codici fiscali italiani, partite IVA italiane, IBAN e indirizzi IP. Questa soluzione è più veloce e non ha dipendenze esterne.

**Benefici in Termini di Performance:**
-   **Dipendenze Ridotte**: Eliminate 3 pacchetti esterni pesanti.
-   **Avvio più Veloce**: Non è necessario caricare modelli spaCy.
-   **Minore Impronta di Memoria**: L'elaborazione basata su regex utilizza meno memoria.
-   **Maggiore Affidabilità**: Nessuna dipendenza da file di modelli esterni.
-   **Deployment più Semplice**: Meno dipendenze da gestire.

Tutti i guardrail esistenti continuano a funzionare correttamente.

### Guardrails di Input

Questi guardrail processano la richiesta dell'utente prima che venga inviata al modello AI.

-   **Input Length Control**: Controlla la lunghezza dell'input per prevenire attacchi di tipo Denial of Service (DoS) e ottimizzare le performance. Respinge o tronca i messaggi eccessivamente lunghi.
-   **Language Detection**: Rileva la lingua dell'input per assicurarsi che sia tra quelle supportate e applica policy specifiche per ogni lingua.
-   **PII Filter**: Identifica e anonimizza informazioni personali sensibili (es. email, numeri di telefono, codici fiscali) per proteggere la privacy dell'utente e garantire la conformità al GDPR.
-   **Prompt Injection**: Rileva e blocca i tentativi di "prompt injection", in cui un utente malintenzionato cerca di manipolare il comportamento del modello AI con istruzioni nascoste.

### Guardrails di Output

Questi guardrail analizzano la risposta generata dal modello AI prima che venga mostrata all'utente.

-   **Harmful Content Filter**: Filtra contenuti dannosi, offensivi o inappropriati (es. violenza, incitamento all'odio, disinformazione). Se rileva un problema, sostituisce la risposta con un messaggio sicuro.
-   **Bias & Fairness Mitigation**: Analizza l'output per identificare e correggere bias legati a genere, etnia o età, promuovendo un linguaggio equo e inclusivo.

## Modifiche al Prompt del Chatbot

### Obiettivo
Modificare il prompt del chatbot per evitare frasi che rivelano la sua natura AI, come "in base ai documenti in mio possesso", "in base al contesto", ecc. Il chatbot deve comportarsi come un assistente tecnico qualificato e professionale.

### Modifiche Apportate

#### 1. Modifica del Prompt Principale
**File:** `pdf_chatbot_prodotti.py` (righe 364-400)

**Prima:**
```
Sei un assistente virtuale specializzato nell'analisi di manuali tecnici...
```

**Dopo:**
```
Sei un esperto tecnico specializzato in questo prodotto. Hai una conoscenza approfondita di tutte le specifiche tecniche, procedure e caratteristiche del prodotto...
```

##### Cambiamenti chiave:
- Cambiato da "assistente virtuale" a "esperto tecnico"
- Aggiunto che "conosce direttamente" il prodotto
- Inclusi esempi specifici di frasi da evitare
- Aggiunti esempi di risposte corrette

#### 2. Istruzioni Specifiche Aggiunte
- **NON usare mai:** "Gentile utente", "in base alla documentazione", "secondo i manuali", ecc.
- **Usare invece:** "Per questo problema...", "La procedura corretta è...", "Ti consiglio di..."
- **Esempi concreti** di cosa fare e cosa evitare

#### 3. Funzione di Post-Processing
**Aggiunta:** `_clean_ai_references()` (righe 414-455)

Questa funzione rimuove automaticamente:
- "Gentile utente"
- "in base alla documentazione"
- "in base ai documenti"
- "secondo i manuali"
- "dalle informazioni disponibili"
- "in base al contesto"
- E molte altre varianti

##### Caratteristiche:
- Usa regex per identificare pattern problematici
- Pulisce spazi multipli e virgole orfane
- Capitalizza automaticamente la prima lettera
- Gestisce casi edge come virgole iniziali

#### 4. Modifica del Messaggio di Errore
**Prima:**
```
❌ Non ho trovato informazioni rilevanti nei documenti per questo prodotto.
```

**Dopo:**
```
❌ Mi dispiace, non riesco a trovare informazioni specifiche per rispondere alla tua domanda su questo prodotto.
```

### File di Test Creati

#### 1. `test_prompt_changes.py`
- Testa la funzione `_clean_ai_references()`
- Verifica che le frasi problematiche vengano rimosse correttamente
- Include 5 test cases diversi

#### 2. `test_chatbot_responses.py`
- Testa il chatbot completo (se le API key sono configurate)
- Verifica che le risposte non contengano frasi problematiche
- Fornisce istruzioni per test manuali

### Come Testare le Modifiche

#### Test Automatici
```bash
# Test della funzione di pulizia
python test_prompt_changes.py

# Test del chatbot completo (richiede API keys)
python test_chatbot_responses.py
```

#### Test Manuali
1. Configura le API keys nel file `.env`
2. Aggiungi documenti PDF in `pdf/CODICE_PRODOTTO/`
3. Avvia l'applicazione: `python app.py`
4. Testa varie domande nell'interfaccia web
5. Verifica che le risposte non contengano frasi come:
   - "Gentile utente"
   - "in base alla documentazione"
   - "secondo i manuali"
   - "dalle informazioni disponibili"

### Risultato Atteso

#### Prima delle Modifiche
```
Gentile utente, in base alla documentazione disponibile, 
il prodotto funziona nel seguente modo...
```

#### Dopo le Modifiche
```
Per questo prodotto, la procedura di funzionamento prevede...
```

### Note Tecniche

- Le modifiche sono retrocompatibili
- Non influenzano altre funzionalità del chatbot
- Il post-processing è efficiente e non rallenta le risposte
- I link ai documenti vengono mantenuti correttamente
- La funzione di pulizia è case-insensitive

### Monitoraggio

Per verificare l'efficacia delle modifiche:
1. Monitora le risposte del chatbot in produzione
2. Raccogli feedback dagli utenti
3. Aggiungi nuovi pattern alla funzione di pulizia se necessario
4. Considera di aggiungere logging per tracciare le sostituzioni effettuate

### Possibili Miglioramenti Futuri

1. **Machine Learning**: Usare un modello per identificare automaticamente frasi "robotiche"
2. **A/B Testing**: Confrontare le risposte prima e dopo le modifiche
3. **Feedback Loop**: Permettere agli utenti di segnalare risposte che sembrano "artificiali"
4. **Personalizzazione**: Adattare il tono in base al tipo di utente o prodotto

## Configurazione Avanzata

### Livelli di Verbosità

Puoi modificare il comportamento del chatbot cambiando il valore di `VERBOSITY_LEVEL` nel file `.env`:

-   **1 (Conciso)**: Risposte brevi e dirette.
-   **2 (Poco Dettagliato)**: Risposte con brevi spiegazioni.
-   **3 (Bilanciato)**: Il giusto equilibrio tra dettaglio e concisione (default).
-   **4 (Articolato)**: Risposte complete che approfondiscono l'argomento.
-   **5 (Esaustivo)**: Spiegazioni approfondite con esempi e contesto aggiuntivo.

## Endpoint API

L'applicazione espone alcuni endpoint API per la gestione della chat:

-   `POST /prepare`
    -   Prepara i documenti per un dato codice prodotto.
    -   **Payload**: `{"product_code": "CODICE_X"}`
    -   **Risposta**: `{"success": true/false, "message": "..."}`

-   `POST /chat`
    -   Invia un messaggio al chatbot e riceve una risposta.
    -   **Payload**: `{"message": "...", "product_code": "CODICE_X", "history": [...]}`
    -   **Risposta**: `{"answer": "...", "history": [...]}`

# Correzione Bug: Link ai Documenti Rimossi dalle Risposte

## Problema Identificato

I riferimenti ai documenti nelle risposte dell'assistente virtuale non avevano più i link cliccabili. I documenti venivano citati ma senza i link diretti al documento e alla pagina specifica.

## Causa del Problema

Il bug era causato da un errore nella gestione dei risultati dei guardrails di output nel file `pdf_chatbot_prodotti.py`, riga 509:

```python
# CODICE ERRATO
answer = output_result.get("modified_text", answer)
```

Il problema era che:
1. Il `GuardrailManager` restituisce sempre un campo `text` che contiene il testo processato (modificato o originale)
2. Il campo `modified_text` esiste solo quando i guardrails modificano effettivamente il testo
3. Quando i guardrails non modificano il testo, `modified_text` è `None` o non esiste
4. Il codice cercava `modified_text` invece di `text`, causando la perdita del contenuto processato

## Soluzione Implementata

Modificata la riga 509 in `pdf_chatbot_prodotti.py`:

```python
# CODICE CORRETTO
answer = output_result.get("text", answer)
```

Questa modifica garantisce che:
- Venga sempre utilizzato il campo `text` che contiene il testo processato dai guardrails
- I link ai documenti vengano preservati correttamente
- Il sistema funzioni sia quando i guardrails modificano il testo sia quando non lo modificano

## Struttura del GuardrailManager

Il `GuardrailManager.process_output()` restituisce sempre un oggetto con questa struttura:

```python
{
    "text": "testo processato (modificato o originale)",
    "original_text": "testo originale",
    "blocked": False,
    "modified": False,
    "warnings": [],
    "metadata": {...}
}
```

## Test di Verifica

### Test Manuale
Creato e eseguito `test_links_preservation.py` che:
- Inizializza il chatbot
- Esegue una query che genera riferimenti ai documenti
- Verifica che i link siano presenti nella risposta
- **Risultato**: ✅ 13 link ai documenti trovati e preservati

### Test di Regressione
Creato `tests/test_document_links_regression.py` con 4 test:
1. `test_links_preserved_in_response` - Verifica preservazione link in risposte reali
2. `test_guardrails_dont_remove_links` - Verifica che i guardrails non rimuovano i link
3. `test_output_result_structure` - Verifica struttura corretta del risultato guardrails
4. `test_multiple_links_preservation` - Verifica preservazione di più link nella stessa risposta

**Risultato**: ✅ Tutti i test passati

## Formato dei Link ai Documenti

I link ai documenti hanno il formato:
```markdown
[📄 Nome_File.pdf - Pag. X](/pdf/CodiceProducto/Nome_File.pdf#page=X)
```

Esempio:
```markdown
[📄 Symphony ST125 200 E5 Manuale uso e manutenzione.pdf - Pag. 17](/pdf/ProdottoA/Symphony%20ST125%20200%20E5%20Manuale%20uso%20e%20manutenzione.pdf#page=17)
```

## Funzioni Coinvolte

1. **`_create_file_link()`** - Genera il link al documento
2. **`_generate_answer()`** - Crea i riferimenti con link nei context_parts
3. **`_clean_ai_references()`** - Protegge i link durante la pulizia del testo
4. **`search_and_answer()`** - Gestisce il flusso completo e applica i guardrails

## Prevenzione Futura

- Aggiunto test di regressione automatico
- Documentato il comportamento corretto del GuardrailManager
- Chiarito l'uso dei campi `text` vs `modified_text`

## Verifica della Correzione

La correzione è stata verificata sia:
- **Programmaticamente**: tramite test automatici
- **Nell'interfaccia web**: testato manualmente nel browser
- **Nei log**: confermato che i link vengono preservati durante tutto il flusso

## File Modificati

- `pdf_chatbot_prodotti.py` (riga 509)
- `test_links_preservation.py` (nuovo)
- `tests/test_document_links_regression.py` (nuovo)
- `tests/__init__.py` (nuovo)

## Stato

✅ **RISOLTO** - I link ai documenti vengono ora preservati correttamente in tutte le risposte dell'assistente virtuale.