/**
 * Sistema di modale fullscreen per l'apertura dei link
 * Gestisce navigazione, cronologia, loader e gestione errori
 */

class LinkModal {
    constructor() {
        this.modal = null;
        this.iframe = null;
        this.loader = null;
        this.errorContainer = null;
        this.titleElement = null;
        this.backBtn = null;
        this.forwardBtn = null;
        this.refreshBtn = null;
        this.closeBtn = null;
        this.retryBtn = null;
        this.overlay = null;
        
        // Cronologia di navigazione
        this.history = [];
        this.currentIndex = -1;
        this.currentUrl = '';
        
        // Timeout per il caricamento
        this.loadTimeout = null;
        this.maxLoadTime = 15000; // 15 secondi
        this.isLoaded = false; // Flag per tracciare se l'iframe è caricato
        
        this.init();
    }
    
    init() {
        this.modal = document.getElementById('link-modal');
        this.iframe = document.getElementById('modal-iframe');
        this.loader = document.getElementById('modal-loader');
        this.errorContainer = document.getElementById('modal-error');
        this.titleElement = document.getElementById('modal-title');
        this.backBtn = document.getElementById('modal-back-btn');
        this.forwardBtn = document.getElementById('modal-forward-btn');
        this.refreshBtn = document.getElementById('modal-refresh-btn');
        this.closeBtn = document.getElementById('modal-close-btn');
        this.retryBtn = document.getElementById('modal-retry-btn');
        this.overlay = this.modal?.querySelector('.modal-overlay');
        
        if (!this.modal) {
            console.warn('Link modal elements not found');
            return;
        }
        
        this.bindEvents();
    }
    
    bindEvents() {
        // Eventi dei pulsanti
        this.closeBtn?.addEventListener('click', () => this.close());
        this.backBtn?.addEventListener('click', () => this.goBack());
        this.forwardBtn?.addEventListener('click', () => this.goForward());
        this.refreshBtn?.addEventListener('click', () => this.refresh());
        this.retryBtn?.addEventListener('click', () => this.retry());
        
        // Chiusura con click sull'overlay
        this.overlay?.addEventListener('click', () => this.close());
        
        // Gestione tasti di scelta rapida e focus trap
        document.addEventListener('keydown', (e) => {
            if (!this.isOpen()) return;

            // Focus trap - mantieni il focus all'interno della modale
            if (e.key === 'Tab') {
                this.handleFocusTrap(e);
            }

            switch(e.key) {
                case 'Escape':
                    this.close();
                    break;
                case 'F5':
                    e.preventDefault();
                    this.refresh();
                    break;
                case 'ArrowLeft':
                    if (e.altKey) {
                        e.preventDefault();
                        this.goBack();
                    }
                    break;
                case 'ArrowRight':
                    if (e.altKey) {
                        e.preventDefault();
                        this.goForward();
                    }
                    break;
            }
        });
        
        // Eventi dell'iframe
        if (this.iframe) {
            this.iframe.addEventListener('load', () => this.onIframeLoad());
            this.iframe.addEventListener('error', () => this.onIframeError());
        }
        
        // Intercetta i click sui link nei messaggi
        document.addEventListener('click', (e) => {
            if (e.target.tagName === 'A' && e.target.href) {
                e.preventDefault();
                this.openLink(e.target.href, e.target.textContent || e.target.href);
            }
        });
    }
    
    openLink(url, title = '') {
        if (!url || !this.modal) return;
        
        this.currentUrl = url;
        this.showModal();
        this.showLoader();
        this.hideError();
        this.hideIframe();
        
        // Aggiorna il titolo
        this.updateTitle(title || 'Caricamento...');
        
        // Aggiungi alla cronologia se è un nuovo URL
        if (this.currentIndex === -1 || this.history[this.currentIndex] !== url) {
            // Rimuovi gli elementi successivi se stiamo navigando da una posizione intermedia
            this.history = this.history.slice(0, this.currentIndex + 1);
            this.history.push(url);
            this.currentIndex = this.history.length - 1;
        }
        
        this.updateNavigationButtons();
        this.loadUrl(url);
    }
    
    loadUrl(url) {
        if (!this.iframe) return;

        this.clearLoadTimeout();
        this.isLoaded = false; // Resetta lo stato di caricamento

        // Valida l'URL
        if (!this.isValidUrl(url)) {
            this.onIframeError('URL non valido');
            return;
        }

        // Imposta timeout per il caricamento
        this.loadTimeout = setTimeout(() => {
            this.onLoadTimeout();
        }, this.maxLoadTime);

        try {
            this.iframe.data = url; // Usa .data per l'object
        } catch (error) {
            this.onIframeError('Errore nel caricamento del link: ' + error.message);
        }
    }

    isValidUrl(string) {
        // Se l'URL inizia con '/', consideralo un URL relativo valido
        if (string.startsWith('/')) {
            return true;
        }
        
        // Altrimenti, controlla se è un URL assoluto valido
        try {
            const url = new URL(string);
            return url.protocol === 'http:' || url.protocol === 'https';
        } catch (_) {
            return false;
        }
    }
    
    onIframeLoad() {
        this.clearLoadTimeout();

        // Verifica se il contenuto è stato effettivamente caricato
        try {
            const iframeDoc = this.iframe.contentDocument || this.iframe.contentWindow.document;

            // Se riusciamo ad accedere al documento, verifica se è vuoto o ha errori
            if (iframeDoc && iframeDoc.body) {
                const bodyText = iframeDoc.body.textContent || '';

                // Controlla se la pagina contiene messaggi di errore comuni
                if (bodyText.toLowerCase().includes('404') ||
                    bodyText.toLowerCase().includes('not found') ||
                    bodyText.toLowerCase().includes('page not found')) {
                    this.onIframeError('Pagina non trovata (404)');
                    return;
                }

                // Prova a ottenere il titolo dalla pagina caricata
                const pageTitle = iframeDoc.title;
                if (pageTitle && pageTitle.trim()) {
                    this.updateTitle(pageTitle);
                }
            }
        } catch (error) {
            // Errore di CORS - non possiamo accedere al contenuto
            // Questo è normale per molti siti, mantieni il titolo corrente
            console.log('CORS restriction - cannot access iframe content');
        }

        this.isLoaded = true; // Marca come caricato con successo
        this.hideLoader();
        this.hideError();
        this.showIframe();
    }
    
    onIframeError(message = 'Errore di caricamento') {
        this.clearLoadTimeout();
        this.hideLoader();
        this.hideIframe();

        // Personalizza il messaggio di errore in base al tipo
        let errorMessage = message;
        let suggestions = '';

        if (message.includes('Timeout')) {
            suggestions = 'La pagina potrebbe essere lenta o non disponibile. Puoi provare ad aprirla in una nuova scheda.';
        } else if (message.includes('404') || message.includes('not found')) {
            suggestions = 'La pagina richiesta non esiste o è stata spostata.';
        } else if (message.includes('URL non valido')) {
            suggestions = 'L\'indirizzo fornito non è corretto.';
        } else {
            suggestions = 'Il sito potrebbe bloccare la visualizzazione in iframe. Puoi provare ad aprirlo in una nuova scheda.';
        }

        this.showError(errorMessage, suggestions);

        // Aggiungi pulsante per aprire in nuova scheda
        this.addOpenInNewTabButton();
    }

    onLoadTimeout() {
        // Verifica se l'iframe è già stato caricato con successo
        if (this.isLoaded) {
            return;
        }
        this.onIframeError('Timeout: il caricamento della pagina ha richiesto troppo tempo');
    }
    
    goBack() {
        if (this.currentIndex > 0) {
            this.currentIndex--;
            const url = this.history[this.currentIndex];
            this.currentUrl = url;
            this.showLoader();
            this.hideError();
            this.hideIframe();
            this.updateNavigationButtons();
            this.loadUrl(url);
        }
    }
    
    goForward() {
        if (this.currentIndex < this.history.length - 1) {
            this.currentIndex++;
            const url = this.history[this.currentIndex];
            this.currentUrl = url;
            this.showLoader();
            this.hideError();
            this.hideIframe();
            this.updateNavigationButtons();
            this.loadUrl(url);
        }
    }
    
    refresh() {
        if (this.currentUrl) {
            this.showLoader();
            this.hideError();
            this.hideIframe();
            this.loadUrl(this.currentUrl);
        }
    }
    
    retry() {
        this.refresh();
    }
    
    close() {
        if (this.modal) {
            this.modal.classList.add('hidden');
            this.clearLoadTimeout();

            // Ripristina il focus precedente
            if (this.previousFocus) {
                this.previousFocus.focus();
                this.previousFocus = null;
            }

            // Ripristina lo scroll del body
            document.body.style.overflow = '';

            // Reset dopo l'animazione
            setTimeout(() => {
                if (this.iframe) {
                    this.iframe.data = 'about:blank'; // Usa .data per l'object
                }
                this.hideLoader();
                this.hideError();
                this.hideIframe();
                this.updateTitle('');
                this.isLoaded = false; // Reset dello stato di caricamento

                // Reset della cronologia
                this.history = [];
                this.currentIndex = -1;
                this.currentUrl = null;
                this.updateNavigationButtons();
            }, 300);
        }
    }
    
    showModal() {
        if (this.modal) {
            this.modal.classList.remove('hidden');

            // Gestione del focus per accessibilità
            this.previousFocus = document.activeElement;

            // Sposta il focus sul pulsante di chiusura
            setTimeout(() => {
                if (this.closeBtn) {
                    this.closeBtn.focus();
                }
            }, 100);

            // Previeni lo scroll del body
            document.body.style.overflow = 'hidden';
        }
    }
    
    isOpen() {
        return this.modal && !this.modal.classList.contains('hidden');
    }
    
    showLoader() {
        if (this.loader) {
            this.loader.classList.remove('hidden');

            // Aggiorna il testo del loader con informazioni sul caricamento
            const loaderText = this.loader.querySelector('.loader-text');
            if (loaderText) {
                loaderText.textContent = 'Caricamento in corso...';

                // Simula un progresso di caricamento
                let dots = 0;
                const loadingInterval = setInterval(() => {
                    if (this.loader.classList.contains('hidden')) {
                        clearInterval(loadingInterval);
                        return;
                    }

                    dots = (dots + 1) % 4;
                    loaderText.textContent = 'Caricamento in corso' + '.'.repeat(dots);
                }, 500);
            }
        }
    }
    
    hideLoader() {
        if (this.loader) {
            this.loader.classList.add('hidden');
        }
    }
    
    showIframe() {
        if (this.iframe) {
            this.iframe.classList.remove('hidden');
        }
    }
    
    hideIframe() {
        if (this.iframe) {
            this.iframe.classList.add('hidden');
        }
    }
    
    showError(message, suggestions = '') {
        if (this.errorContainer) {
            this.errorContainer.classList.remove('hidden');
            const messageElement = document.getElementById('modal-error-message');
            if (messageElement) {
                let fullMessage = message;
                if (suggestions) {
                    fullMessage += '\n\n' + suggestions;
                }
                messageElement.textContent = fullMessage;
            }
        }
    }
    
    hideError() {
        if (this.errorContainer) {
            this.errorContainer.classList.add('hidden');

            // Rimuovi il pulsante "Apri in nuova scheda" se presente
            const newTabBtn = this.errorContainer.querySelector('.open-new-tab-btn');
            if (newTabBtn) {
                newTabBtn.remove();
            }
        }
    }
    
    updateTitle(title) {
        if (this.titleElement) {
            this.titleElement.textContent = title;
        }
    }
    
    updateNavigationButtons() {
        if (this.backBtn) {
            this.backBtn.disabled = this.currentIndex <= 0;
        }
        if (this.forwardBtn) {
            this.forwardBtn.disabled = this.currentIndex >= this.history.length - 1;
        }
    }
    
    clearLoadTimeout() {
        if (this.loadTimeout) {
            clearTimeout(this.loadTimeout);
            this.loadTimeout = null;
        }
    }

    shouldOpenInModal(url) {
        // Apri nella modale se:
        // 1. È un file PDF
        // 2. È un link dello stesso dominio
        // 3. È un link a documentazione comune (github, docs, etc.)

        const pathname = url.pathname.toLowerCase();
        const hostname = url.hostname.toLowerCase();

        // File PDF
        if (pathname.endsWith('.pdf')) {
            return true;
        }

        // Stesso dominio
        if (hostname === window.location.hostname) {
            return true;
        }

        // Domini di documentazione comuni
        const docDomains = [
            'docs.google.com',
            'github.com',
            'gitlab.com',
            'bitbucket.org',
            'stackoverflow.com',
            'developer.mozilla.org',
            'w3schools.com'
        ];

        return docDomains.some(domain => hostname.includes(domain));
    }

    addOpenInNewTabButton() {
        const errorContainer = this.errorContainer;
        if (!errorContainer || !this.currentUrl) return;

        // Rimuovi pulsante esistente se presente
        const existingBtn = errorContainer.querySelector('.open-new-tab-btn');
        if (existingBtn) {
            existingBtn.remove();
        }

        // Crea nuovo pulsante
        const newTabBtn = document.createElement('button');
        newTabBtn.className = 'open-new-tab-btn';
        newTabBtn.textContent = 'Apri in nuova scheda';
        newTabBtn.style.cssText = `
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 10px 20px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            margin-left: 10px;
            transition: background-color 0.2s ease;
        `;

        newTabBtn.addEventListener('click', () => {
            window.open(this.currentUrl, '_blank');
            this.close();
        });

        newTabBtn.addEventListener('mouseenter', () => {
            newTabBtn.style.backgroundColor = '#0056b3';
        });

        newTabBtn.addEventListener('mouseleave', () => {
            newTabBtn.style.backgroundColor = '#007bff';
        });

        // Aggiungi il pulsante accanto al pulsante Riprova
        const retryBtn = errorContainer.querySelector('.retry-btn');
        if (retryBtn && retryBtn.parentNode) {
            retryBtn.parentNode.insertBefore(newTabBtn, retryBtn.nextSibling);
        }
    }

    handleFocusTrap(e) {
        if (!this.modal) return;

        // Ottieni tutti gli elementi focusabili nella modale
        const focusableElements = this.modal.querySelectorAll(
            'button:not([disabled]), [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );

        const firstFocusable = focusableElements[0];
        const lastFocusable = focusableElements[focusableElements.length - 1];

        if (e.shiftKey) {
            // Shift + Tab - vai all'elemento precedente
            if (document.activeElement === firstFocusable) {
                e.preventDefault();
                lastFocusable.focus();
            }
        } else {
            // Tab - vai all'elemento successivo
            if (document.activeElement === lastFocusable) {
                e.preventDefault();
                firstFocusable.focus();
            }
        }
    }
}

// Inizializza la modale quando il DOM è pronto
document.addEventListener('DOMContentLoaded', () => {
    window.linkModal = new LinkModal();
});

// Inizializza la modale quando il DOM è pronto
document.addEventListener('DOMContentLoaded', () => {
    window.linkModal = new LinkModal();
});
